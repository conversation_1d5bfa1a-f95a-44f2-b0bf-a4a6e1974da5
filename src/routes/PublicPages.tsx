import ErrorBoundaryAndSuspense from '../common/components/ErrorBoundaryAndSuspense'
import LoginForm from '../features/authentication/pages/LoginForm'
import PinResetPage from '../features/authentication/pages/PinResetPage'
import RegisterPage from '../features/authentication/pages/RegisterPage'
import Home from '../features/dashboard/components/Home'
import CalculatorForms from '../features/dashboard/pages/CalculatorForms'
import SandboxTontinatorPage from '../features/dashboard/pages/SandboxTontinatorPage'
import { PUBLIC } from './Route'

const MyTontinePages = [
  {
    path: PUBLIC.PIN_RESET,
    page: <PinResetPage />,
  },
  {
    path: PUBLIC.SIGN_IN,
    page: (
      <ErrorBoundaryAndSuspense>
        <LoginForm />
      </ErrorBoundaryAndSuspense>
    ),
  },
  {
    path: PUBLIC.SIGN_UP,
    page: <RegisterPage />,
  },
  {
    path: PUBLIC.SIGN_UP_REFERRAL,
    page: <RegisterPage />,
  },
  {
    path: PUBLIC.TONTINATOR,
    page: <CalculatorForms />,
  },
  {
    path: PUBLIC.HOME,
    page: <Home />,
  },
  {
    path: PUBLIC.NOT_FOUND_404,
    page: <Home />,
  },
  {
    path: PUBLIC.PUBLIC_TONTINATOR,
    page: <SandboxTontinatorPage />,
  },
]

const PublicPages = [...MyTontinePages] as const

export { PublicPages }
